# proxy_config_dialog.py - Enhanced with Webshare API integration

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QCheckBox,
    QLineEdit, QPushButton, QLabel, QTabWidget, QWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QRadioButton, QButtonGroup, QGroupBox, QSpinBox, QProgressBar
)
from PyQt5.QtCore import Qt, QSettings, QThread, pyqtSignal
import requests
import json
import logging

logger = logging.getLogger("webook_pro")


class WebshareProxyLoaderThread(QThread):
    """Thread to load proxies from Webshare API"""
    progress_signal = pyqtSignal(int, str)  # value, message
    result_signal = pyqtSignal(list)  # list of proxies
    error_signal = pyqtSignal(str)  # error message

    def __init__(self, api_token):
        super().__init__()
        self.api_token = api_token

    def run(self):
        try:
            proxies = []
            self.progress_signal.emit(10, "Connecting to Webshare API...")

            # Set headers with API key
            headers = {
                "Authorization": f"Token {self.api_token}"
            }

            # First, get a download token for the proxy list
            self.progress_signal.emit(20, "Requesting download token...")
            try:
                response = requests.get(
                    "https://proxy.webshare.io/api/v2/download_token/proxy_list/",
                    headers=headers
                )

                if response.status_code != 200 and response.status_code != 201:
                    self.error_signal.emit(f"API error when requesting download token: {response.status_code} - {response.text}")
                    return

                data = response.json()
                download_token = data.get("key")  # The token is in the 'key' field
            except Exception as e:
                logger.error(f"Exception when requesting download token: {str(e)}")
                self.error_signal.emit(f"Exception when requesting download token: {str(e)}")
                return

            if not download_token:
                self.error_signal.emit("Failed to get download token from API response")
                return

            self.progress_signal.emit(40, f"Got download token. Downloading all proxies at once...")

            # Use the download token to get all proxies at once
            try:
                response = requests.get(
                    f"https://proxy.webshare.io/api/v2/proxy/list/download/{download_token}/-/any/username/direct/-/",
                    headers=headers
                )

                if response.status_code != 200:
                    self.error_signal.emit(f"API error when downloading proxies: {response.status_code} - {response.text}")
                    return

                # The response should be a text file with one proxy per line
                # Format: IP:PORT:USERNAME:PASSWORD
                proxy_lines = response.text.strip().split('\n')
                total_proxies = len(proxy_lines)

                self.progress_signal.emit(70, f"Processing {total_proxies} proxies...")
            except Exception as e:
                logger.error(f"Exception when downloading proxies: {str(e)}")
                self.error_signal.emit(f"Exception when downloading proxies: {str(e)}")
                return

            if total_proxies == 0:
                self.error_signal.emit("No proxies found in the download")
                return

            self.progress_signal.emit(70, f"Processing {total_proxies} proxies...")

            # Process all proxies
            for proxy_line in proxy_lines:
                parts = proxy_line.strip().split(':')
                if len(parts) >= 4:
                    proxy_str = proxy_line.strip()
                    # We don't have country and city info in this format, but we can add placeholders
                    proxies.append({
                        "proxy_str": proxy_str,
                        "country": "",  # We don't have this info from the download endpoint
                        "city": "",     # We don't have this info from the download endpoint
                        "last_verification": "",  # We don't have this info from the download endpoint
                        "valid": True
                    })

            self.progress_signal.emit(100, f"Downloaded {len(proxies)} proxies successfully!")
            self.result_signal.emit(proxies)

        except Exception as e:
            logger.error(f"Error loading proxies from Webshare API: {str(e)}")
            self.error_signal.emit(f"Error: {str(e)}")

class ProxyConfigDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configure Proxy")
        self.setModal(True)
        self.resize(700, 500)  # Make dialog larger to accommodate the tabs

        # Initialize QSettings (using your organization and application name)
        self.settings = QSettings()

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.single_proxy_tab = QWidget()
        self.proxy_list_tab = QWidget()

        self.tab_widget.addTab(self.single_proxy_tab, "Single Proxy")
        self.tab_widget.addTab(self.proxy_list_tab, "Proxy List")

        # Setup single proxy tab
        self.setup_single_proxy_tab()

        # Setup proxy list tab
        self.setup_proxy_list_tab()

        # Main layout
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tab_widget)

        # Buttons at the bottom
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Wire up OK/Cancel
        self.ok_button.clicked.connect(self._save_settings)
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)

        # Load saved settings
        self._load_settings()

        # Connect tab change signal to update proxy mode radio buttons
        self.tab_widget.currentChanged.connect(self._on_tab_changed)

    def setup_single_proxy_tab(self):
        """Setup the single proxy tab interface"""
        layout = QVBoxLayout()

        # Create the checkbox to enable/disable proxy
        self.enable_checkbox = QCheckBox("Enable Proxy?")

        # Create the line edits (with default values)
        self.domain_edit = QLineEdit()
        self.port_edit = QLineEdit()
        self.username_edit = QLineEdit()
        self.password_edit = QLineEdit()

        # Radio button for rotation mode
        rotation_group = QGroupBox("Rotation Mode")
        rotation_layout = QVBoxLayout()

        self.rotation_radio_group = QButtonGroup(self)
        self.use_rotating_radio = QRadioButton("Use Webshare's rotation (username-rotate)")
        self.use_direct_radio = QRadioButton("Direct connection (no rotation)")

        self.rotation_radio_group.addButton(self.use_rotating_radio, 1)
        self.rotation_radio_group.addButton(self.use_direct_radio, 2)

        rotation_layout.addWidget(self.use_rotating_radio)
        rotation_layout.addWidget(self.use_direct_radio)
        rotation_group.setLayout(rotation_layout)

        # Lay them out
        form_layout = QFormLayout()
        form_layout.addRow(self.enable_checkbox)
        form_layout.addRow("Domain Name:", self.domain_edit)
        form_layout.addRow("Proxy Port:", self.port_edit)
        form_layout.addRow("Proxy Username:", self.username_edit)
        form_layout.addRow("Proxy Password:", self.password_edit)
        form_layout.addRow(rotation_group)

        layout.addLayout(form_layout)
        self.single_proxy_tab.setLayout(layout)

        # Connect checkbox to toggling the fields
        self.enable_checkbox.stateChanged.connect(self._on_checkbox_changed)

    def setup_proxy_list_tab(self):
        """Setup the proxy list tab interface"""
        layout = QVBoxLayout()

        # API Token section
        api_layout = QHBoxLayout()
        self.api_token_edit = QLineEdit()
        self.api_token_edit.setPlaceholderText("Enter your Webshare API token")
        # self.api_token_edit.setEchoMode(QLineEdit.Password)  # Mask the token
        self.load_proxies_btn = QPushButton("Load Proxies")
        self.load_proxies_btn.clicked.connect(self._load_proxies_from_api)

        api_layout.addWidget(QLabel("Webshare API Token:"))
        api_layout.addWidget(self.api_token_edit, 1)
        api_layout.addWidget(self.load_proxies_btn)

        layout.addLayout(api_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_label = QLabel("Ready")

        progress_layout = QVBoxLayout()
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)
        layout.addLayout(progress_layout)

        # Proxy list settings
        options_layout = QHBoxLayout()

        # Rotation interval
        rotation_layout = QHBoxLayout()
        rotation_layout.addWidget(QLabel("Rotate every:"))
        self.rotation_count_spin = QSpinBox()
        self.rotation_count_spin.setRange(1, 1000)
        self.rotation_count_spin.setValue(50)
        rotation_layout.addWidget(self.rotation_count_spin)
        rotation_layout.addWidget(QLabel("requests"))

        options_layout.addLayout(rotation_layout)
        options_layout.addStretch()

        # Country filter (future enhancement)
        # self.country_filter_edit = QLineEdit()
        # self.country_filter_edit.setPlaceholderText("Filter by country code (comma separated)")
        # options_layout.addWidget(QLabel("Filter Countries:"))
        # options_layout.addWidget(self.country_filter_edit)

        layout.addLayout(options_layout)

        # Proxy list table
        self.proxy_table = QTableWidget()
        self.proxy_table.setColumnCount(5)
        self.proxy_table.setHorizontalHeaderLabels(["Proxy", "Country", "City", "Last Verified", "Use"])

        # Set column widths
        header = self.proxy_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        layout.addWidget(self.proxy_table)

        # Enable local rotation checkbox
        self.enable_local_rotation = QCheckBox("Enable Local Proxy Rotation")
        self.enable_local_rotation.setChecked(True)
        layout.addWidget(self.enable_local_rotation)

        self.proxy_list_tab.setLayout(layout)

        # Initialize proxy list
        self.proxy_list = []

    def _on_tab_changed(self, index):
        """Handle tab change to update proxy mode"""
        if index == 0:  # Single proxy tab
            # Update the proxy mode radio buttons based on the username
            username = self.username_edit.text()
            if "rotate" in username:
                self.use_rotating_radio.setChecked(True)
            else:
                self.use_direct_radio.setChecked(True)

    def _on_checkbox_changed(self, state):
        enabled = (state == Qt.Checked)
        self._set_single_proxy_fields_enabled(enabled)

    def _set_single_proxy_fields_enabled(self, enabled):
        """Enable/disable proxy fields based on checkbox."""
        self.domain_edit.setEnabled(enabled)
        self.port_edit.setEnabled(enabled)
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.use_rotating_radio.setEnabled(enabled)
        self.use_direct_radio.setEnabled(enabled)

    def _load_settings(self):
        """Load proxy settings from persistent storage."""
        enabled = self.settings.value("proxy/enabled", False, type=bool)
        domain = self.settings.value("proxy/domain", "p.webshare.io")
        port = self.settings.value("proxy/port", "80")
        username = self.settings.value("proxy/username", "taplmftg-rotate")
        password = self.settings.value("proxy/password", "")

        # Load rotation mode setting
        use_rotating = self.settings.value("proxy/use_rotating", True, type=bool)

        # Load API token
        api_token = self.settings.value("proxy/api_token", "")
        self.api_token_edit.setText(api_token)

        # Load rotation count
        rotation_count = self.settings.value("proxy/rotation_count", 50, type=int)
        self.rotation_count_spin.setValue(rotation_count)

        # Load local rotation setting
        local_rotation = self.settings.value("proxy/local_rotation", True, type=bool)
        self.enable_local_rotation.setChecked(local_rotation)

        # Load proxy list
        try:
            proxy_list_json = self.settings.value("proxy/proxy_list", "[]")
            self.proxy_list = json.loads(proxy_list_json)
            self._update_proxy_table()
        except Exception as e:
            logger.error(f"Error loading proxy list: {str(e)}")
            self.proxy_list = []

        # Set values in UI
        self.enable_checkbox.setChecked(enabled)
        self.domain_edit.setText(domain)
        self.port_edit.setText(port)
        self.username_edit.setText(username)
        self.password_edit.setText(password)

        # Set rotation radio button
        if use_rotating:
            self.use_rotating_radio.setChecked(True)
        else:
            self.use_direct_radio.setChecked(True)

        # Enable/disable fields based on the loaded checkbox state
        self._set_single_proxy_fields_enabled(enabled)

    def _save_settings(self):
        """Save the current proxy settings to persistent storage."""
        # Save single proxy settings
        self.settings.setValue("proxy/enabled", self.enable_checkbox.isChecked())
        self.settings.setValue("proxy/domain", self.domain_edit.text().strip())
        self.settings.setValue("proxy/port", self.port_edit.text().strip())

        # Update username based on rotation mode
        username = self.username_edit.text().strip()
        if self.use_rotating_radio.isChecked() and not username.endswith("-rotate"):
            # Append -rotate if using rotation mode
            if "-" in username:
                username = username.split("-")[0] + "-rotate"
            else:
                username = username + "-rotate"
        elif self.use_direct_radio.isChecked() and username.endswith("-rotate"):
            # Remove -rotate if using direct mode
            username = username.replace("-rotate", "")

        self.settings.setValue("proxy/username", username)
        self.settings.setValue("proxy/password", self.password_edit.text())

        # Save rotation mode
        self.settings.setValue("proxy/use_rotating", self.use_rotating_radio.isChecked())

        # Save API token
        self.settings.setValue("proxy/api_token", self.api_token_edit.text())

        # Save rotation count
        self.settings.setValue("proxy/rotation_count", self.rotation_count_spin.value())

        # Save local rotation setting
        self.settings.setValue("proxy/local_rotation", self.enable_local_rotation.isChecked())

        # Save proxy list
        try:
            # Update 'use' flags based on checkboxes
            for row in range(self.proxy_table.rowCount()):
                check_item = self.proxy_table.item(row, 4)
                if check_item and row < len(self.proxy_list):
                    self.proxy_list[row]["use"] = check_item.checkState() == Qt.Checked

            proxy_list_json = json.dumps(self.proxy_list)
            self.settings.setValue("proxy/proxy_list", proxy_list_json)
        except Exception as e:
            logger.error(f"Error saving proxy list: {str(e)}")

    def _load_proxies_from_api(self):
        """Load proxies from Webshare API"""
        api_token = self.api_token_edit.text().strip()
        if not api_token:
            QMessageBox.warning(self, "Error", "Please enter your Webshare API token")
            return

        # Disable button while loading
        self.load_proxies_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Connecting to API...")

        # Start loader thread
        self.loader_thread = WebshareProxyLoaderThread(api_token)
        self.loader_thread.progress_signal.connect(self._update_progress)
        self.loader_thread.result_signal.connect(self._process_proxy_list)
        self.loader_thread.error_signal.connect(self._handle_api_error)
        self.loader_thread.finished.connect(lambda: self.load_proxies_btn.setEnabled(True))
        self.loader_thread.start()

    def _update_progress(self, value, message):
        """Update progress bar and label"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)

    def _process_proxy_list(self, proxies):
        """Process the list of proxies returned from the API"""
        self.proxy_list = proxies

        # Add 'use' flag to each proxy (default to True)
        for proxy in self.proxy_list:
            if "use" not in proxy:
                proxy["use"] = True

        # Update the table
        self._update_proxy_table()

        # Show summary
        QMessageBox.information(
            self,
            "Proxies Loaded",
            f"Successfully loaded {len(proxies)} proxies from Webshare API."
        )

    def _handle_api_error(self, error_message):
        """Handle API error"""
        QMessageBox.critical(self, "API Error", error_message)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Failed to load proxies")

    def _update_proxy_table(self):
        """Update the proxy table with current proxy list"""
        self.proxy_table.setRowCount(len(self.proxy_list))

        for row, proxy in enumerate(self.proxy_list):
            # Proxy string - mask the password
            proxy_parts = proxy["proxy_str"].split(":")
            if len(proxy_parts) >= 4:
                masked_proxy = f"{proxy_parts[0]}:{proxy_parts[1]}:{proxy_parts[2]}:{'*' * len(proxy_parts[3])}"
            else:
                masked_proxy = proxy["proxy_str"]

            self.proxy_table.setItem(row, 0, QTableWidgetItem(masked_proxy))
            self.proxy_table.setItem(row, 1, QTableWidgetItem(proxy.get("country", "")))
            self.proxy_table.setItem(row, 2, QTableWidgetItem(proxy.get("city", "")))
            self.proxy_table.setItem(row, 3, QTableWidgetItem(proxy.get("last_verification", "")))

            # Checkbox for "Use" column
            use_item = QTableWidgetItem()
            use_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            use_item.setCheckState(Qt.Checked if proxy.get("use", True) else Qt.Unchecked)
            self.proxy_table.setItem(row, 4, use_item)

    def get_proxy_settings(self):
        """
        Return a dict with all proxy settings.
        """
        # Determine proxy mode
        current_tab = self.tab_widget.currentIndex()

        settings = {
            "enabled": self.enable_checkbox.isChecked() if current_tab == 0 else True,
            "mode": "single" if current_tab == 0 else "list",
            "domain": self.domain_edit.text().strip(),
            "port": self.port_edit.text().strip(),
            "username": self.username_edit.text().strip(),
            "password": self.password_edit.text(),
            "use_rotating": self.use_rotating_radio.isChecked(),
            "api_token": self.api_token_edit.text(),
            "rotation_count": self.rotation_count_spin.value(),
            "local_rotation": self.enable_local_rotation.isChecked(),
            "proxy_list": [p for p in self.proxy_list if p.get("use", True)]
        }

        return settings


# Test function for direct testing
def test_proxy_loading(api_token):
    """Test the proxy loading functionality directly"""
    import sys
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QObject, pyqtSlot

    class SignalHandler(QObject):
        @pyqtSlot(int, str)
        def on_progress(self, value, message):
            print(f"{message} ({value}%)")

        @pyqtSlot(list)
        def on_result(self, proxies):
            print(f"\nSuccessfully downloaded {len(proxies)} proxies!")
            if proxies:
                print("First 3 proxies (masked passwords):")
                for i, proxy in enumerate(proxies[:3]):
                    # Mask the password in the proxy string
                    proxy_parts = proxy["proxy_str"].split(":")
                    if len(proxy_parts) >= 4:
                        masked_proxy = f"{proxy_parts[0]}:{proxy_parts[1]}:{proxy_parts[2]}:{'*' * len(proxy_parts[3])}"
                    else:
                        masked_proxy = proxy["proxy_str"]
                    print(f"  {i+1}. {masked_proxy} - {proxy.get('country', '')}, {proxy.get('city', '')}")

        @pyqtSlot(str)
        def on_error(self, error_message):
            print(f"\nError: {error_message}")

    # Create QApplication instance if needed
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)

    # Create signal handler
    handler = SignalHandler()

    # Create and start the loader thread
    loader_thread = WebshareProxyLoaderThread(api_token)
    loader_thread.progress_signal.connect(handler.on_progress)
    loader_thread.result_signal.connect(handler.on_result)
    loader_thread.error_signal.connect(handler.on_error)
    loader_thread.start()

    # Run event loop for 60 seconds max
    import time
    start_time = time.time()
    while loader_thread.isRunning() and time.time() - start_time < 60:
        app.processEvents()
        time.sleep(0.1)

    return loader_thread.isFinished()


# If run directly, test with the provided token
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        api_token = sys.argv[1]
        print(f"Testing proxy loading with token: {api_token[:5]}...{api_token[-5:]}")
        test_proxy_loading(api_token)
    else:
        api_token = "qhj5cg3a2ok95rs0m0ut1iskt77pt4q1gk8oz4er"
        print(f"Testing proxy loading with default token: {api_token[:5]}...{api_token[-5:]}")
        test_proxy_loading(api_token)
