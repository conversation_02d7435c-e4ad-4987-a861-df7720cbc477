import json
import time
import logging
import hashlib

import random
from PyQt5.QtCore import QObject, pyqtSignal
import concurrent.futures
from urllib.parse import urljoin
from collections import deque
import queue
import statistics
from functools import lru_cache
import threading

from helper import get_http_client, make_request

from chart_token_manager import get_chart_token
from token_retrieval import get_hold_token as new_get_hold_token, get_cached_event_id

logger = logging.getLogger('webook_pro')

# Global connection pool and persistent clients
global_executor = concurrent.futures.ThreadPoolExecutor(
    max_workers=20,
    thread_name_prefix="FastHold"
)

# Pre-generated request templates
CACHED_BROWSER_IDS = [''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)) for _ in range(50)]
CACHED_USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
]


@lru_cache(maxsize=1024)  # Increased cache size for better hit rate
def generate_x_signature_local(body: str) -> str:
    """Ultra-fast signature generation with optimizations"""
    try:
        # Get the current chart token - cached internally
        token = get_chart_token()
        if not token:
            # Fallback to empty token signature
            return hashlib.sha256(body.encode("utf-8")).hexdigest()
            
        # Reverse the token
        reversed_token = token[::-1]
        
        # Concatenate and hash in one go
        return hashlib.sha256((reversed_token + body).encode("utf-8")).hexdigest()
    except Exception:
        # Fast fallback without logging
        return hashlib.sha256(body.encode("utf-8")).hexdigest()

class FastHoldManager(QObject):
    """Ultra-optimized FastHoldManager for sub-40ms holds"""
    
    seat_held_signal = pyqtSignal(str, str)
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)

    def __init__(self, event_key, chart_key, channel_keys, team_id, proxy=None, thread_pool_size=20, event_id=None):
        super().__init__()
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.team_id = team_id
        self.proxy = proxy
        self.token = None
        self.expire_time = None
        self.event_id = event_id
        
        self.metrics = {
            "response_times": deque(maxlen=100),
            "success_count": 0,
            "failure_count": 0
        }
        self.metrics_lock = threading.Lock()

        # Pre-prepare templates for faster execution
        self._prepare_request_templates()

        # Get initial token
        token_success = self._get_initial_token()
        if not token_success:
            self.log_signal.emit("⚠️ Failed to get initial token - autohold may not work properly")



    def _build_optimized_channel_keys(self):
        """Build channel keys matching helper.py logic exactly"""
        # Use the same logic as helper.py build_channel_keys function
        if not self.channel_keys or self.channel_keys == ['NO_CHANNEL']:
            return ['NO_CHANNEL']

        keys = []
        if isinstance(self.channel_keys, dict):
            if 'common' in self.channel_keys and self.channel_keys['common']:
                keys.extend(['NO_CHANNEL', self.channel_keys['common'][0]])
                if self.team_id and self.team_id in self.channel_keys and self.channel_keys[self.team_id]:
                    keys.append(self.channel_keys[self.team_id][0])
                else:
                    for k, v in self.channel_keys.items():
                        if k != 'common' and v:
                            keys.append(v[0])
                            break

        if not keys:
            keys.append('NO_CHANNEL')
            if self.team_id and isinstance(self.channel_keys, dict) and self.team_id in self.channel_keys and self.channel_keys[self.team_id]:
                keys.append(self.channel_keys[self.team_id][0])

        return keys


    def _get_initial_token(self):
        """Get initial token using the optimized method"""
        try:
            # Get event ID from cache
            event_id = get_cached_event_id()
            if not event_id:
                self.log_signal.emit("⚠️ No event ID available, waiting for event to load")
                return False

            # Get new hold token with timeout
            start_time = time.time()
            hold_token = new_get_hold_token(event_id=event_id, proxy=self.proxy)
            token_time = (time.time() - start_time) * 1000

            if not hold_token:
                self.log_signal.emit(f"❌ Failed to get token in {token_time:.1f}ms")
                return False

            self.token = hold_token

            # Activate the token to get expiration time (with timeout)
            from helper import activate_hold_token
            activation_start = time.time()
            time_left = activate_hold_token(hold_token, self.proxy)
            activation_time = (time.time() - activation_start) * 1000

            self.expire_time = time.time() + time_left

            total_time = token_time + activation_time
            self.log_signal.emit(f"✅ Token {self.token[:8]}... ready in {total_time:.1f}ms")
            return True

        except Exception as e:
            self.log_signal.emit(f"❌ Token error: {str(e)}")
            return False

    def _build_proxy_config(self):
        """Pre-build proxy configuration for HTTP requests"""
        if not self.proxy:
            return None
            
        parts = self.proxy.split(":")
        if len(parts) == 4:
            proxy_host, proxy_port, proxy_user, proxy_pass = parts
            return {
                "http://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}",
            }
        elif len(parts) == 2:
            proxy_host, proxy_port = parts
            return {
                "http://": f"http://{proxy_host}:{proxy_port}",
                "https://": f"http://{proxy_host}:{proxy_port}",
            }
        return None


    def _prepare_request_templates(self):
        """Pre-compute common request components for maximum performance"""
        self.hold_url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        self.optimized_keys = self._build_optimized_channel_keys()
        self.proxies = self._build_proxy_config()
        self.base_headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'priority': 'u=1, i',
            'referer': 'https://cdn-eu.seatsio.net/static/version/seatsio-ui-prod-00221-fff/chart-renderer/chartRendererIframe.html?environment=PROD',
            'user-agent': CACHED_USER_AGENTS[0],
            'x-client-tool': 'Renderer',
            'x-kl-saas-ajax-request': 'Ajax_Request',
        }
        self.request_template = {
            'events': [self.event_key],
            'holdToken': None,
            'objects': [{'objectId': None}],
            'channelKeys': self.optimized_keys,
            'validateEventsLinkedToSameChart': True,
        }
    
    def hold_seat(self, seat_number):
        """Ultra-optimized seat holding with maximum performance"""
        global_executor.submit(self._hold_seat_sync, seat_number)
        return True

    def _hold_seat_sync(self, seat_number):
        """Optimized synchronous seat holder with minimal overhead"""
        self._hold_seat_optimized(seat_number)

    def _is_token_expired(self):
        """Check if token is expired or about to expire"""
        if not self.token or not self.expire_time:
            return True
        # Consider token expired if less than 30 seconds remaining
        return time.time() >= (self.expire_time - 30)

    def _hold_seat_optimized(self, seat_number):
        """Ultra-fast synchronous seat holding implementation"""
        start_time = time.time()
        success = False
        max_retries = 2

        for attempt in range(max_retries):
            try:
                # Check token expiration proactively
                if self._is_token_expired():
                    logger.debug(f"Token expired, refreshing for seat {seat_number}")
                    if not self._get_initial_token():
                        logger.error(f"Failed to refresh token for seat {seat_number}")
                        return False

                token = self.token
                if not token:
                    logger.error(f"No token available for seat {seat_number}")
                    return False

                json_data = self.request_template.copy()
                json_data['holdToken'] = token
                json_data['objects'] = [{'objectId': seat_number}]

                x_sig = generate_x_signature_local(json.dumps(json_data, separators=(',', ':')))

                headers = self.base_headers.copy()
                headers['x-browser-id'] = random.choice(CACHED_BROWSER_IDS)
                headers['user-agent'] = random.choice(CACHED_USER_AGENTS)
                headers['x-signature'] = x_sig

                network_start_time = time.time()

                # Use the optimized HTTP client wrapper from helper.py
                from helper import get_http_client
                http_client = get_http_client(verify=False)

                response = http_client.post(
                    self.hold_url,
                    json=json_data,
                    headers=headers,
                    timeout=3.0  # Increased timeout for better reliability
                )

                network_elapsed_ms = (time.time() - network_start_time) * 1000

                if response.status_code == 204:
                    success = True
                    with self.metrics_lock:
                        self.metrics["response_times"].append(network_elapsed_ms)
                        self.metrics["success_count"] += 1

                    self.performance_signal.emit({
                        'avg_ms': network_elapsed_ms,
                        'success': True,
                        'seat_id': seat_number,
                        'operation': 'hold'
                    })

                    logger.info(f"✅ Held seat {seat_number} in {network_elapsed_ms:.2f}ms with token {token[:8]}...")
                    self.seat_held_signal.emit(seat_number, self.token)
                    return True

                elif response.status_code == 403:
                    # Token might be invalid, try to get a new one
                    logger.warning(f"403 error for seat {seat_number}, refreshing token")
                    self._get_initial_token()
                    if attempt < max_retries - 1:
                        continue

                else:
                    logger.warning(f"Failed to hold seat {seat_number}: {response.status_code} - {response.text[:100]}")

                with self.metrics_lock:
                    self.metrics["failure_count"] += 1

                self.performance_signal.emit({
                    'avg_ms': network_elapsed_ms,
                    'success': False,
                    'seat_id': seat_number,
                    'operation': 'hold'
                })

                # If not a token issue, don't retry
                if response.status_code != 403:
                    break

            except Exception as e:
                logger.error(f"Exception holding seat {seat_number}: {str(e)}")
                if attempt < max_retries - 1:
                    continue
                break

        # Final failure handling
        if not success:
            total_elapsed_ms = (time.time() - start_time) * 1000
            logger.error(f"❌ Failed to hold seat {seat_number} after {max_retries} attempts in {total_elapsed_ms:.2f}ms")

        return success

    def cleanup(self):
        """Clean up resources when done"""
        if self._loop and self._loop.is_running():
            self._loop.call_soon_threadsafe(self._loop.stop)

    def renew_token_if_needed(self):
        """Renew token if needed using the new method"""
        if self.token and self.expire_time and (self.expire_time - time.time() < 480):  # 8 minutes
            try:
                # Get event ID from cache
                event_id = get_cached_event_id()
                if not event_id:
                    self.log_signal.emit("No event ID available for token renewal")
                    return

                # Get new hold token
                hold_token = new_get_hold_token(event_id=event_id, proxy=self.proxy)
                if not hold_token:
                    self.log_signal.emit("Failed to renew token")
                    return

                self.token = hold_token

                # Activate the token
                from helper import activate_hold_token
                time_left = activate_hold_token(hold_token, self.proxy)
                self.expire_time = time.time() + time_left

                self.log_signal.emit(f"Token renewed: {self.token[:8]}...")
            except Exception as e:
                self.log_signal.emit(f"Error renewing token: {str(e)}")

@lru_cache(maxsize=1024)  # Increased cache size for better hit rate
def generate_x_signature_local(body: str) -> str:
    """Ultra-fast signature generation with optimizations"""
    try:
        # Get the current chart token - cached internally
        token = get_chart_token()
        if not token:
            # Fallback to empty token signature
            return hashlib.sha256(body.encode("utf-8")).hexdigest()
            
        # Reverse the token
        reversed_token = token[::-1]
        
        # Concatenate and hash in one go
        return hashlib.sha256((reversed_token + body).encode("utf-8")).hexdigest()
    except Exception:
        # Fast fallback without logging
        return hashlib.sha256(body.encode("utf-8")).hexdigest()
