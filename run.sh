#!/bin/bash

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Path to your Python script and virtual environment
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
SCRIPT_FILE="main.py"
VENV_DIR="${SCRIPT_DIR}/venv"

# List of required libraries
REQUIRED_LIBRARIES=(
    "requests"
    "selenium-stealth"
    "selenium-base"
    "lxml"
    "pyfiglet"
    "httpx"
    "httpx[http2]"
    "httpx[socks]"
    "httpcore[socks]"
    "hwid"
    "pyqt5"
    "tenacity"
)

# Check if Python3 is installed
if ! command_exists python3; then
    echo "Error: Python3 is not installed. Please install Python3 and try again."
    exit 1
fi

# Check if the virtual environment exists, otherwise create it
if [ ! -d "${VENV_DIR}" ]; then
    echo "Creating a virtual environment at ${VENV_DIR}..."
    python3 -m venv "${VENV_DIR}"
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create a virtual environment."
        exit 1
    fi
    echo "Virtual environment created."
fi

# Activate the virtual environment
source "${VENV_DIR}/bin/activate"

# Ensure pip is installed and upgraded
echo "Upgrading pip..."
python -m ensurepip --default-pip
pip install --upgrade pip setuptools wheel

# Install required libraries
echo "Installing required libraries..."
pip install --no-cache-dir "${REQUIRED_LIBRARIES[@]}"
if [ $? -ne 0 ]; then
    echo "Error: Failed to install required libraries."
    deactivate
    exit 1
fi

# Ensure the script file exists and is executable
if [ -f "${SCRIPT_DIR}/${SCRIPT_FILE}" ]; then
    chmod +x "${SCRIPT_DIR}/${SCRIPT_FILE}"
    echo "Running ${SCRIPT_FILE}..."
    python "${SCRIPT_DIR}/${SCRIPT_FILE}"
else
    echo "Error: ${SCRIPT_FILE} not found in ${SCRIPT_DIR}"
    deactivate
    exit 1
fi

# Deactivate the virtual environment
deactivate
