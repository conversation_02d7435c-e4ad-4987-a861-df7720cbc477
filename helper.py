import asyncio
import httpx
import json
import time
import logging
from collections import defaultdict
from typing import List, Dict, Any, <PERSON>tional, Tuple
from chart_token_manager import generate_x_signature as central_generate_x_signature
from token_retrieval import cache_event_id

logger = logging.getLogger("webook_pro")

# --- Configuration ---
SEATSIO_IP = "*************"
SEATSIO_DOMAINS = ["cdn-eu.seatsio.net"]

# --- Centralized Asynchronous HTTP Client Manager ---
_async_clients: Dict[str, httpx.AsyncClient] = {}
_client_lock = asyncio.Lock()

async def get_http_client(proxy: Optional[str] = None) -> httpx.AsyncClient:
    """
    Retrieves a shared httpx.AsyncClient for a given proxy configuration.
    """
    proxy_key = proxy if proxy else "no_proxy"

    async with _client_lock:
        if proxy_key not in _async_clients:
            logger.info(f"Creating new async client for proxy: {proxy_key}")
            client_kwargs = {
                'http2': True,
                'timeout': httpx.Timeout(10.0),
                'verify': False,
                'limits': httpx.Limits(max_connections=500, max_keepalive_connections=100)
            }
            if proxy:
                proxy_url = f"http://{proxy.split(':')[2]}:{proxy.split(':')[3]}@{proxy.split(':')[0]}:{proxy.split(':')[1]}"
                client_kwargs['proxies'] = {'all://': proxy_url}
            
            _async_clients[proxy_key] = httpx.AsyncClient(**client_kwargs)
        return _async_clients[proxy_key]

def _prepare_seatsio_url(url: str) -> Tuple[str, Dict[str, str]]:
    """Optimizes Seats.io URLs by replacing domain with IP and adding Host header."""
    headers = {}
    for domain in SEATSIO_DOMAINS:
        if domain in url:
            url = url.replace(domain, SEATSIO_IP)
            headers['Host'] = domain
            break
    return url, headers

async def async_make_request(method: str, url: str, proxy: Optional[str] = None, **kwargs) -> httpx.Response:
    """The single, centralized function for all asynchronous HTTP requests."""
    client = await get_http_client(proxy)
    
    url, host_header = _prepare_seatsio_url(url)
    
    if 'headers' not in kwargs:
        kwargs['headers'] = {}
    
    kwargs['headers'].update(host_header)
    
    try:
        response = await client.request(method, url, **kwargs)
        return response
    except httpx.RequestError as e:
        logger.error(f"Request failed: {method} {url} via proxy '{proxy}' | Error: {e}")
        return httpx.Response(status_code=0, request=e.request, text=str(e))

# --- Core Asynchronous Seat Management Functions ---

async def hold_seat(
    seat_number: str, 
    event_key: str, 
    hold_token: str, 
    channel_keys: List[str] = None, 
    proxy: Optional[str] = None
) -> bool:
    """Asynchronously holds a single seat."""
    if not channel_keys:
        channel_keys = ['NO_CHANNEL']

    url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    
    json_data = {
        'events': [event_key], 'holdToken': hold_token,
        'objects': [{'objectId': seat_number}], 'channelKeys': channel_keys,
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))
    
    headers = {
        'Host': 'cdn-eu.seatsio.net', 'accept': '*/*',
        'content-type': 'application/json', 'origin': 'https://cdn-eu.seatsio.net',
        'x-signature': central_generate_x_signature(body_str)
    }
    
    response = await async_make_request('POST', url, proxy=proxy, content=body_str, headers=headers)
    
    if response.status_code == 204:
        logger.info(f"Successfully held seat {seat_number} with token {hold_token[:8]}...")
        return True
    else:
        logger.error(f"Failed to hold seat {seat_number}. Status: {response.status_code}, Msg: {response.text[:100]}")
        return False

async def release_seat(seat_number: str, event_key: str, hold_token: str, proxy: Optional[str] = None) -> bool:
    """Asynchronously releases a single seat."""
    url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects'
    
    json_data = {
        'events': [event_key], 'holdToken': hold_token,
        'objects': [{'objectId': seat_number}],
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))
    
    headers = {
        'Host': 'cdn-eu.seatsio.net', 'accept': '*/*',
        'content-type': 'application/json', 'origin': 'https://cdn-eu.seatsio.net',
        'x-signature': central_generate_x_signature(body_str)
    }
    
    response = await async_make_request('POST', url, proxy=proxy, content=body_str, headers=headers)
    return response.status_code == 204

async def switch_seat_immediate(
    seat_number: str, 
    event_key: str, 
    old_token: str, 
    new_token: str,
    channel_keys: List[str] = None,
    proxy: Optional[str] = None
) -> bool:
    """Performs a high-speed, near-simultaneous release and hold operation."""
    if not channel_keys:
        channel_keys = ['NO_CHANNEL']

    release_url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects'
    release_json = {'events': [event_key], 'holdToken': old_token, 'objects': [{'objectId': seat_number}]}
    release_body = json.dumps(release_json, separators=(',', ':'))
    release_headers = {'Host': 'cdn-eu.seatsio.net', 'content-type': 'application/json', 'x-signature': central_generate_x_signature(release_body)}

    hold_url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    hold_json = {'events': [event_key], 'holdToken': new_token, 'objects': [{'objectId': seat_number}], 'channelKeys': channel_keys}
    hold_body = json.dumps(hold_json, separators=(',', ':'))
    hold_headers = {'Host': 'cdn-eu.seatsio.net', 'content-type': 'application/json', 'x-signature': central_generate_x_signature(hold_body)}

    start_time = time.perf_counter()
    release_task = asyncio.create_task(async_make_request('POST', release_url, proxy=proxy, content=release_body, headers=release_headers))
    hold_task = asyncio.create_task(async_make_request('POST', hold_url, proxy=proxy, content=hold_body, headers=hold_headers))

    release_resp, hold_resp = await asyncio.gather(release_task, hold_task)
    total_time = (time.perf_counter() - start_time) * 1000

    if hold_resp.status_code == 204:
        logger.info(f"✅ SWITCHED seat {seat_number} in {total_time:.2f}ms")
        return True
    else:
        logger.error(f"❌ FAILED to switch seat {seat_number}. Release: {release_resp.status_code}, Hold: {hold_resp.status_code} ({hold_resp.text[:80]})")
        return False

# --- Utility & Processing Functions ---
class VM:
    @staticmethod
    def deobfuscate(e, t):
        i = 63 & VM.hash_code(t)
        n = bytearray(e)
        for j in range(len(n)):
            n[j] = (n[j] - i) & 0xFF
        return n.decode('utf-8')

    @staticmethod
    def hash_code(e):
        t = 0
        for i in range(len(e)):
            t = (29 * t % 10007 + ord(e[i])) % 10007
        return t

def group_tickets_by_type_and_status(data, free_only=False):
    ticket_groups = defaultdict(lambda: defaultdict(dict))
    for entry in data:
        seat_id = entry.get('objectLabelOrUuid')
        if not seat_id:
            continue
        status = entry.get('status', 'free').lower()
        if free_only and status != 'free':
            continue
        ticket_type = seat_id.split('-')[0].strip() if '-' in seat_id else seat_id
        if not ticket_type: ticket_type = "UNKNOWN"
        ticket_groups[ticket_type][status][seat_id] = entry
    return ticket_groups

async def get_object_statuses(event_key: str, chart_key: str, proxy: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Asynchronously gets object statuses from seatsio API.
    """
    try:
        url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/{event_key}/objects/status'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = await async_make_request('GET', url, proxy=proxy, headers=headers)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'objects' in data:
                return data['objects']
            elif isinstance(data, list):
                return data
            else:
                logger.error(f"Unexpected response format from get_object_statuses: {type(data)}")
                return []
        else:
            logger.error(f"Failed to get object statuses: {response.status_code} - {response.text[:100]}")
            return []
    except Exception as e:
        logger.error(f"Error getting object statuses: {str(e)}")
        return []

async def get_event_seatsio_info(data: Dict[str, Any], proxy: Optional[str] = None) -> Dict[str, Any]:
    """
    Gets additional seatsio event information.
    """
    try:
        event_key = data.get('event_key')
        chart_key = data.get('chart_key')
        if not event_key or not chart_key:
            logger.error("Missing event_key or chart_key in data")
            return {}

        url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/{event_key}'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = await async_make_request('GET', url, proxy=proxy, headers=headers)

        if response.status_code == 200:
            event_info = response.json()
            logger.info(f"Successfully retrieved seatsio event info for {event_key}")
            return event_info
        else:
            logger.error(f"Failed to get seatsio event info: {response.status_code} - {response.text[:100]}")
            return {}
    except Exception as e:
        logger.error(f"Error getting seatsio event info: {str(e)}")
        return {}

async def activate_hold_token(hold_token: str, proxy: Optional[str] = None) -> int:
    """
    Activates a hold token and returns the time left in seconds.
    """
    try:
        url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{hold_token}/activate'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = await async_make_request('POST', url, proxy=proxy, headers=headers, json={})
        if response.status_code == 200:
            data = response.json()
            time_left = data.get('expiresInSeconds', 900)
            logger.info(f"Activated hold token {hold_token[:8]}... with {time_left} seconds remaining")
            return time_left
        else:
            logger.error(f"Failed to activate hold token: {response.status_code} - {response.text[:100]}")
            return 900
    except Exception as e:
        logger.error(f"Error activating hold token: {str(e)}")
        return 900