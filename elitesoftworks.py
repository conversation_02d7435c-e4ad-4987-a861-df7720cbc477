from colorama import Fore, Style
import requests
import sys
import pyfiglet
import os
import subprocess
import hwid


def print_logo(version='', script_name='', identifier='', machine_id=''):
    logo = r"""
    This is a product of EliteSoftWorks
 ___        ___  ___  __   __   ___ ___       __   __        __  
|__  |    |  |  |__  /__` /  \ |__   |  |  | /  \ |__) |__/ /__` 
|___ |___ |  |  |___ .__/ \__/ |     |  |/\| \__/ |  \ |  \ .__/ 
    
    """
    stop_script_with_flag(script_name, version, identifier, machine_id)
    print(Style.BRIGHT + f"{Fore.GREEN}{logo}")

    if script_name:
        ascii_art_name = pyfiglet.figlet_format(script_name)
        print(Fore.BLUE + ascii_art_name)
    
    if version:
        ascii_art_version = pyfiglet.figlet_format(f"Version {version}")
        print(Fore.YELLOW + ascii_art_version)
    
    print(Style.DIM + f"{Fore.RED}Telegram : https://t.me/NoodlesRush")
    print(f"{Fore.BLUE}Website : https://elitesoftworks.com")
    print(Style.RESET_ALL, Fore.RESET)
    print("\n\n")

def get_machine_hwid():
    try:
        return hwid.get_hwid()
    except Exception as e:
        print(f"Error retrieving machine hardware ID: {e}")
        return ""
# def get_machine_hwid():
#     try:
#         if os.name == 'nt':  # Windows
#             machine_id = subprocess.check_output(
#                 ['wmic', 'csproduct', 'get', 'uuid'], text=True
#             ).split('\n')[1].strip()
        
#         elif os.uname().sysname == 'Darwin':  # macOS
#             machine_id = subprocess.check_output(
#                 shlex.split("ioreg -rd1 -c IOPlatformExpertDevice | grep IOPlatformUUID"),
#                 text=True
#             ).split('"')[-2].strip()

#         else:  # Linux
#             possible_paths = ["/var/lib/dbus/machine-id", "/etc/machine-id"]
#             machine_id = None
#             for path in possible_paths:
#                 if os.path.exists(path):
#                     with open(path, "r") as f:
#                         machine_id = f.read().strip()
#                     break

#             if machine_id is None:
#                 raise RuntimeError("Machine ID file not found on Linux.")

#         return machine_id
#     except Exception as e:
#         print(f"Error retrieving machine hardware ID: {e}")
#         return None

def stop_script_with_flag(script_name = '', version = '', identifier = '', machine_id = ''):
    if not machine_id:
        machine_id = get_machine_hwid()
    try:
        stop_script = requests.get(f'https://elitesoftworks.com/stop_script?script_name={script_name}&version={version}&identifier={identifier}&machine_id={machine_id}', timeout=3)
        if stop_script.json()['stop_script']:
            print("Script needs updating. Exiting...")
            print("Please download the latest version from https://elitesoftworks.com or contact us at https://t.me/NoodlesRush")
            input("Press Enter to exit...")
            sys.exit()
    except Exception as e:
        # print(f"Failed to check for updates")
        pass
