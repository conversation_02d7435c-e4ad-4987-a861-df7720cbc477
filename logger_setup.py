import logging
from logging.handlers import RotatingFileHandler
import os
import getpass


def setup_logger():
    """
    Initialize the logging configuration for the entire application.
    Modified to disable noisy debug logs.
    Also logs to a file in the 'logs' directory, with a separate file for each user.
    """
    format_string = (
        "[%(levelname)s] %(asctime)s (%(filename)s:%(lineno)d) - %(message)s"
    )

    # Create logger and set the lowest level you want to capture
    logger = logging.getLogger("webook_pro")
    logger.setLevel(logging.INFO)  # Changed from DEBUG to INFO
    logger.propagate = False

    # Create console handler with a higher log level
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(format_string))
    logger.addHandler(console_handler)
    
    # Get the current user's name
    username = getpass.getuser()
    # Ensure the 'logs' directory exists
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    user_log_dir = os.path.join(log_dir, username)
    os.makedirs(user_log_dir, exist_ok=True)
    

    # Create a rotating file handler with UTF-8 encoding
    file_handler = RotatingFileHandler(
        os.path.join(user_log_dir, "webook_pro.log"),
        maxBytes=10 * 1024 * 1024,
        backupCount=5,
        encoding="utf-8",
    )
    file_handler.setLevel(logging.INFO)  # Changed from DEBUG to INFO
    file_handler.setFormatter(logging.Formatter(format_string))

    logger.addHandler(file_handler)
    
    # Silence noisy third-party loggers
    silence_noisy_loggers()
    
    return logger

def silence_noisy_loggers():
    """Silence overly verbose third-party loggers"""
    # Set websockets library to only show warnings and above
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("websockets.protocol").setLevel(logging.WARNING)
    logging.getLogger("websockets.connection").setLevel(logging.WARNING)
    
    # Silence httpx and urllib3 loggers (used for HTTP requests)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Silence asyncio debug logs
    logging.getLogger("asyncio").setLevel(logging.WARNING)

if __name__ == "__main__":
    logger = setup_logger()
    logger.info("This is an info message.")
    logger.warning("This is a warning message.")
    logger.error("This is an error message with special char: \u274c")
    logger.critical("This is a critical message.")
    # Debug messages will now be suppressed
    logger.debug("This debug message should not appear.")