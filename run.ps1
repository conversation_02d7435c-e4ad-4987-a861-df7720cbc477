# PowerShell script to set up environment and run main.py
# Intended to be run by double-clicking from Windows Explorer

# Function to check if a command exists
function Test-CommandExists {
    param ($Command)
    $oldPreference = $ErrorActionPreference
    $ErrorActionPreference = 'stop'
    try {
        if (Get-Command $Command) { return $true }
    }
    catch {
        return $false
    }
    finally {
        $ErrorActionPreference = $oldPreference
    }
}

# Get script directory and set paths
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$scriptFile = "main.py"
$venvDir = Join-Path $scriptDir "venv"

# Required libraries array
$requiredLibraries = @(
    "requests",
    "selenium-stealth",
    "selenium-base",
    "lxml",
    "pyfiglet",
    "httpx",
    "httpx[http2]",
    "httpx[socks]",
    "httpcore[socks]",
    "hwid",
    "pyqt5",
    "tenacity"
)

# Check if Python is installed
if (-not (Test-CommandExists python)) {
    Write-Host "Error: Python is not installed or not in PATH. Please install Python and try again." -ForegroundColor Red
    Read-Host -Prompt "Press Enter to exit"
    exit 1
}

# Check Python version
$pythonVersion = (python --version 2>&1).ToString()
Write-Host "Using $pythonVersion"

# Check if the virtual environment exists, otherwise create it
if (-not (Test-Path $venvDir)) {
    Write-Host "Creating a virtual environment at $venvDir..." -ForegroundColor Cyan
    python -m venv $venvDir
    if (-not $?) {
        Write-Host "Error: Failed to create a virtual environment." -ForegroundColor Red
        Read-Host -Prompt "Press Enter to exit"
        exit 1
    }
    Write-Host "Virtual environment created." -ForegroundColor Green
}

# Activate the virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Cyan
& "$venvDir\Scripts\Activate.ps1"

# Check if activation worked
if (-not $?) {
    Write-Host "Error: Failed to activate the virtual environment." -ForegroundColor Red
    Read-Host -Prompt "Press Enter to exit"
    exit 1
}

# Ensure pip is installed and upgraded
Write-Host "Upgrading pip..." -ForegroundColor Cyan
python -m ensurepip --default-pip
python -m pip install --upgrade pip setuptools wheel

# Install required libraries
Write-Host "Installing required libraries..." -ForegroundColor Cyan
foreach ($lib in $requiredLibraries) {
    Write-Host "Installing $lib..." -ForegroundColor Yellow
    python -m pip install --no-cache-dir $lib
    if (-not $?) {
        Write-Host "Error: Failed to install $lib." -ForegroundColor Red
        deactivate
        Read-Host -Prompt "Press Enter to exit"
        exit 1
    }
}

# Ensure the script file exists and run it
$scriptPath = Join-Path $scriptDir $scriptFile
if (Test-Path $scriptPath) {
    Write-Host "Running $scriptFile..." -ForegroundColor Green
    python $scriptPath
    $scriptExitCode = $LASTEXITCODE
} else {
    Write-Host "Error: $scriptFile not found in $scriptDir" -ForegroundColor Red
    deactivate
    Read-Host -Prompt "Press Enter to exit"
    exit 1
}

# Deactivate the virtual environment
Write-Host "Deactivating virtual environment..." -ForegroundColor Cyan
deactivate

# Report completion status
if ($scriptExitCode -eq 0) {
    Write-Host "`nScript execution completed successfully." -ForegroundColor Green
} else {
    Write-Host "`nScript execution completed with errors (exit code: $scriptExitCode)." -ForegroundColor Red
}

Read-Host -Prompt "Press Enter to exit"