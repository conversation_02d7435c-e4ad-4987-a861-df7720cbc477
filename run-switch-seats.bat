@echo off
setlocal enabledelayedexpansion

:: Get the directory of this script
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_DIR=%SCRIPT_DIR:~0,-1%"
set "SCRIPT_FILE=seat_switching_system.py"
set "VENV_DIR=%SCRIPT_DIR%\venv"

:: Required libraries
set "REQUIRED_LIBRARIES=requests selenium-stealth selenium-base lxml pyfiglet httpx "httpx[http2]" "httpx[socks]" "httpcore[socks]" hwid pyqt5 tenacity"

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH. Please install Python and try again.
    pause
    exit /b 1
)

:: Check if the virtual environment exists, otherwise create it
if not exist "%VENV_DIR%" (
    echo Creating a virtual environment at %VENV_DIR%...
    python -m venv "%VENV_DIR%"
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to create a virtual environment.
        pause
        exit /b 1
    )
    echo Virtual environment created.
)

:: Activate the virtual environment
call "%VENV_DIR%\Scripts\activate.bat"

:: Ensure pip is installed and upgraded
echo Upgrading pip...
python -m ensurepip --default-pip
python -m pip install --upgrade pip setuptools wheel

:: Install required libraries
echo Installing required libraries...
for %%i in (%REQUIRED_LIBRARIES%) do (
    python -m pip install --no-cache-dir %%i
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install %%i.
        call "%VENV_DIR%\Scripts\deactivate.bat"
        pause
        exit /b 1
    )
)

:: Check if the script file exists and run it
if exist "%SCRIPT_DIR%\%SCRIPT_FILE%" (
    echo Running %SCRIPT_FILE%...
    python "%SCRIPT_DIR%\%SCRIPT_FILE%"
) else (
    echo Error: %SCRIPT_FILE% not found in %SCRIPT_DIR%
    call "%VENV_DIR%\Scripts\deactivate.bat"
    pause
    exit /b 1
)

:: Deactivate the virtual environment
call "%VENV_DIR%\Scripts\deactivate.bat"

:: Keep the window open if there's an error
if %ERRORLEVEL% neq 0 (
    echo.
    echo Script execution completed with errors.
    pause
) else (
    echo.
    echo Script execution completed successfully.
    pause
)

endlocal